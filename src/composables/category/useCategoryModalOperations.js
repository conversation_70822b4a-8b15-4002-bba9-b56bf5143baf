import { useNuxtApp } from "#app";
import { useStore } from "vuex";
import { CONFIRM_MODAL_TYPE } from "@/models/confirm-modal.model.js";
import savePopupImage from "@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png";
import publishVariantIcon from "@/assets/images/publish-variant-icon.png";
import { useSearchStore } from "@/stores/search.js";

export function useCategoryModalOperations(state, saveOperations, selectionOperations, dataOperations, recipeOperations) {
  const { $t } = useNuxtApp();
  const store = useStore();
  const searchStore = useSearchStore();
  
  const CATEGORY_STATUS = {
    ACTIVE: "active",
    HIDDEN: "hidden",
  };

  const generateUniqueSlug = async () => {
    const baseSlug = state.categoriesSlug.value.trim();
    let counter = 1;
    let newSlug = `${baseSlug}-${counter}`;
    let slugExists = true;
    while (slugExists) {
      try {
        const response = await state.categoryStore.checkSlugAsync(newSlug, state.lang.value);

        if (state.props.isEdit && state.categoryISIN.value) {
          slugExists = response?.exists && response?.isin !== state.categoryISIN.value;
        } else {
          slugExists = response?.exists || false;
        }

        if (slugExists) {
          counter++;
          newSlug = `${baseSlug}-${counter}`;
        }
      } catch (error) {
        console.error("[IQ][CategoryForm] Error checking slug uniqueness:", error);
        slugExists = false;
      }
    }
    state.categoriesSlug.value = newSlug;
    state.hasSlugExist.value = false;
  };


  const openPreviewSaveModal = (openModal, closeModal) => {
    if (!state.isCategoryValid.value) return;

    const isPublishing = state.isPublish.value;
    const buttonName = isPublishing ? $t('BUTTONS.PUBLISH_BUTTON') : $t('BUTTONS.SAVE_BUTTON');
    const description = isPublishing
      ? $t('DESCRIPTION_POPUP.PUBLISH_UPDATES_POPUP')
      : $t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP');
    const imageName = isPublishing ? publishVariantIcon : savePopupImage;

    openModal({
      name: "CategorySaveModal",
      props: {
        closeModal: () => closeModal("CategorySaveModal"),
        saveAndPublishFunction: () => saveButtonClickAsync(openModal, closeModal),
        availableLang: [],
        buttonName,
        description,
        imageName,
        slugCheckConfirm: false,
        hasSlugExist: state.hasSlugExist.value,
      },
    });
  };

  const saveButtonClickAsync = async (openModal, closeModal) => {
    try {
      closeModal("CategorySaveModal");

      openModal({
        name: "CategorySavingModal",
        props: {
          status: state.isPublish.value ? "publishing" : "saving",
        },
      });
      if (state.hasSlugExist.value && state.categoriesSlug.value?.trim()) {
        await generateUniqueSlug();
      }

      if (state.props.isEdit && state.categoryISIN.value) {
        await saveOperations.updateCategoryWithVariants();
      } else {
        await saveOperations.createCategoryWithVariants();
      }

      closeModal("CategorySavingModal");

      state.hasChanges.value = false;

      state.router.push(state.backToCategoryPath.value);
    } catch (error) {
      console.error("[IQ][CategoryForm] Error saving category:", error);
      closeModal("CategorySavingModal");
    }
  };

  const deleteCategory = (openModal, closeModal) => {
    openModal({
      name: "confirmModal",
      props: {
        modalType: CONFIRM_MODAL_TYPE.DELETE,
        title: $t('DESCRIPTION_POPUP.DELETE_CATEGORY'),
        description: $t('DESCRIPTION_POPUP.DELETE_CATEGORY_POPUP'),
        confirmBtnLabel: $t('BUTTONS.DELETE_BUTTON'),
      },
      onClose: (response) => {
        if (response) {
          deleteCategoryConfirmed(openModal, closeModal);
        }
      }
    });
  };

  const deleteCategoryConfirmed = async (openModal, closeModal) => {
    try {
      openModal({ name: 'deletingModal' });

      await saveOperations.removeAllCategoryAssociationsAsync(state.categoryISIN.value);
      await state.categoryStore.deleteCategoryList(state.categoryISIN.value);

      closeModal('deletingModal');
      state.hasChanges.value = false;

      state.router.push(state.backToCategoryPath.value);
    } catch (error) {
      console.error("[IQ][CategoryForm] Error deleting category:", error);
      closeModal('deletingModal');
    }
  };

  const publishToggleBtnAsync = async () => {
    state.isCategoriesStatus.value = state.isCategoriesStatus.value === CATEGORY_STATUS.ACTIVE ? CATEGORY_STATUS.HIDDEN : CATEGORY_STATUS.ACTIVE;
    state.isPublish.value = state.isCategoriesStatus.value === CATEGORY_STATUS.ACTIVE;

    if (state.props.isEdit && !state.isInitializing.value) {
      state.hasChanges.value = true;
    }
  };

  const publishToggleBtnPopup = (openModal) => {
    if (state.isCategoriesStatus.value !== CATEGORY_STATUS.ACTIVE) {
      openModal({
        name: "confirmModal",
        props: {
          modalType: CONFIRM_MODAL_TYPE.UNABLE,
          title: $t("TEXT_POPUP.NOT_PUBLISHED"),
          description: $t("TEXT_POPUP.NOT_PUBLISHED_MESSAGE"),
          hideCancelBtn: true
        }
      });
    } else {
      publishToggleBtnAsync();
    }
  };

  const publishToggleConfirmation = (openModal) => {
    const isCurrentlyPublished = state.isCategoriesStatus.value === CATEGORY_STATUS.ACTIVE;
    const modalType = isCurrentlyPublished ? CONFIRM_MODAL_TYPE.UNPUBLISH : CONFIRM_MODAL_TYPE.PUBLISHED;
    if (state.searchConfig.value) {
      openModal({
        name: "confirmModal",
        props: {
          modalType: CONFIRM_MODAL_TYPE.UNABLE,
          title: $t("DESCRIPTION_POPUP.UNABLE_TO_UNPUBLISH"),
          description: $t("DESCRIPTION_POPUP.NOT_PUBLISHED_MESSAGE"),
          hideCancelBtn: true
        }
      });
      return;
    }

    openModal({
      name: "confirmModal",
      props: {
        modalType: modalType,
        title: isCurrentlyPublished
          ? $t('DESCRIPTION_POPUP.UNPUBLISH_CATEGORY_TEXT')
          : $t('DESCRIPTION_POPUP.PUBLISH_CATEGORY_TITLE'),
        descriptionRed: isCurrentlyPublished
          ? $t('DESCRIPTION_POPUP.UNPUBLISH_CATEGORY_DESC')
          : '',
        confirmBtnLabel: isCurrentlyPublished
          ? $t('BUTTONS.UNPUBLISH_BUTTON')
          : $t('BUTTONS.PUBLISH_BUTTON')
      },
      onClose: (response) => {
        if (response) {
          publishToggleBtnAsync();
        }
      }
    });
  };

  const addRecipeToCategory = (openModal, closeModal) => {
    state.recipesWereAddedInModal.value = false;
    searchStore.setSearchQuery("", { emitQueryParam: false, context: 'details-page' });

    openModal({
      name: "CategoryAddRecipeModal",
      props: {
        closeModal: () => handleAddRecipeModalClose(closeModal),
        recipeDataForCategories: state.recipeDataForCategories.value || [],
        categoryPromotedRecipes: state.categoryPromotedRecipes.value || [],
        selectedCategoryRecipe: state.selectedCategoryRecipe.value || [],
        recipesAfterPageChange: state.recipesAfterPageChange.value || [],
        recipeMatchesIsinsTagRemove: state.recipeMatchesIsinsTagRemove.value || [],
        removeRecipeList: [],
        removeRecipeTagList: [],
        isEditCategories: state.props.isEdit,
        isAddCategory: !state.props.isEdit,
        isPageLoading: false,
        isAddTag: false,
        isEditTag: false,
        preventEnterAndSpaceKeyPress: (event) => {
          if (event.key === "Enter" || event.key === " ") {
            event.preventDefault();
          }
        },
        onRecipeAdded: (addedRecipes) => {
          recipeOperations.handleRecipeAdded(addedRecipes);
          state.hasChanges.value = true;
        },
      },
    });
  };

  const handleAddRecipeModalClose = async (closeModal) => {
    closeModal("CategoryAddRecipeModal");
    const categoryIsin = state.props.isEdit ? state.categoryISIN.value : state.newIsin.value;
    if (categoryIsin && state.recipesWereAddedInModal.value) {
      const langValue = store.getters["userData/getDefaultLang"];
      if (state.props.isEdit) {
        const preservedUnpromotedIsins = [...state.totalPromotedRemovedIsin.value];
        await recipeOperations.getPromotedRecipesForCategoriesAsync(categoryIsin, langValue);
        if (preservedUnpromotedIsins.length > 0) {
          state.categoryPromotedRecipes.value = state.categoryPromotedRecipes.value.filter(
            recipe => !preservedUnpromotedIsins.includes(recipe.isin)
          );
          state.categoryPromotedRecipesTotal.value = state.categoryPromotedRecipes.value.length;
          state.totalPromotedRemovedIsin.value = preservedUnpromotedIsins;
        }
      }
      await recipeOperations.getRecipeDataForCategoriesAsync(categoryIsin, langValue);
    }
    state.recipesWereAddedInModal.value = false;
  };

  const handleRecipeAction = async ([actionType, recipeIsin], openModal, closeModal) => {
    const recipe = [
      ...state.categoryPromotedRecipes.value,
      ...state.recipeDataForCategories.value,
    ].find((r) => r.isin === recipeIsin);

    if (!recipe) {
      console.error(
        "[IQ][CategoryForm] Recipe not found for action:",
        actionType,
        recipeIsin
      );
      return;
    }

    switch (actionType) {
      case state.RECIPE_ACTION_CASE.PREVIEW:
        openModal({
          name: "CategoryRecipePreviewModal",
          props: {
            recipeIsin: recipeIsin,
            checkRecipePreviewVideo: () => {},
          },
        });
        break;
      case state.RECIPE_ACTION_CASE.UNPROMOTE:
        await recipeOperations.removePromotedRecipe(recipe);
        break;
      case state.RECIPE_ACTION_CASE.REMOVE:
        state.removeRecipeData.value = recipe;
        openModal({
          name: "CategoryDeleteModal",
          props: {
            closeModal: () => closeModal("CategoryDeleteModal"),
            productInfoTitle: $t('DESCRIPTION_POPUP.REMOVE_RECIPE'),
            productDescriptionOne:
              $t('DESCRIPTION_POPUP.REMOVE_RECIPE_POPUP'),
            productDescriptionTwo:
              $t('DESCRIPTION_POPUP.CATEGORY'),
            deleteItem: () => {
              recipeOperations.removeRecipeFromCategory();
              closeModal("CategoryDeleteModal");
            },
            availableLanguage: 0,
            buttonText: $t("BUTTONS.REMOVE_BUTTON"),
          },
        });
        break;
      default:
        console.warn("[IQ][CategoryForm] Unknown action type:", actionType);
    }
  };

  const deleteSelect = (openModal, closeModal) => {
    if (state.checkSelectedRecipes.value) {
      openModal({
        name: "CategorySelectDeleteModal",
        props: {
          closeModal: () => closeModal("CategorySelectDeleteModal"),
          productInfoTitle: $t('DESCRIPTION_POPUP.REMOVE_RECIPE'),
          productDescriptionOne: $t('DESCRIPTION_POPUP.REMOVE_RECIPES_POPUP'),
          productDescriptionTwo: $t('DESCRIPTION_POPUP.CATEGORY'),
          deleteItem: () => deleteSelectProductMatches(closeModal),
          availableLanguage: 0,
          buttonText: $t('BUTTONS.REMOVE_BUTTON'),
        },
      });
    } else {
      console.warn("[IQ][CategoryForm] No recipes selected for deletion");
    }
  };

  const deleteSelectProductMatches = async (closeModal) => {
    closeModal("CategorySelectDeleteModal");
    await selectionOperations.deleteSelectProductMatches();
  };

  const deleteCategoryVariant = (categoryVariant, index, openModal, closeModal) => {
    dataOperations.deleteCategoryVariant(categoryVariant, index);
    
    openModal({
      name: "CategoryDeleteModal",
      props: {
        closeModal: () => closeModal("CategoryDeleteModal"),
        productInfoTitle: $t('DESCRIPTION_POPUP.REMOVE_CATEGORY_VARIANT'),
        productDescriptionOne: $t('DESCRIPTION_POPUP.REMOVE_CATEGORY_VARIANT_POPUP'),
        deleteItem: () => {
          dataOperations.deleteCategoryVariantAsync();
          closeModal("CategoryDeleteModal");
        },
        availableLanguage: 0,
        buttonText: $t("BUTTONS.REMOVE_BUTTON"),
      },
    });
  };

  return {
    openPreviewSaveModal,
    saveButtonClickAsync,
    deleteCategory,
    deleteCategoryConfirmed,
    publishToggleBtnAsync,
    publishToggleBtnPopup,
    publishToggleConfirmation,
    addRecipeToCategory,
    handleAddRecipeModalClose,
    handleRecipeAction,
    deleteSelect,
    deleteSelectProductMatches,
    deleteCategoryVariant,
  };
}
