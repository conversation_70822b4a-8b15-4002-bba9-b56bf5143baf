<template>
  <content-wrapper wrapper-classes="article-item font-family-averta" :is-body-loading="props.isLoading">
    <page-top-block
      :hint="hintID"
      :page-actions-back-path="'/articles'"
      :background-image="image"
      :page-actions-is-continue-disabled="!isArticleValid || (isEdit && !hasChanges)"
      @page-actions-continue="openPreviewSaveModal"
      @page-actions-cancel="() => navigateTo('/articles')"
    >
      <div class="article-item-head">
        <image-box
          :image-src="image"
          :loaded-image-size="imageLoadedSize"
          :uploadImagePercentage="imageUploadPercentage"
          @uploadedFile="handleFileUpload"
        />
        <div class="article-item-details">
          <div class="article-item-details-top">
            <name-field
              v-model:name="title"
              input-id="article-image"
              :input-placeholder="$t('ARTICLE.NAME_ARTICLE')"
            />

            <button-with-switcher
              class="article-item-details-publish-btn"
              :label="$t('COMMON.PUBLISH')"
              :is-checked="isPublish"
              @action="publishArticleAction"
            />
          </div>

          <div class="article-item-details-body">
            <div class="article-item-details-text font-size-base">
              <span class="font-bold color-grey">{{ $t('ARTICLE.PREVIEW_IMAGE') }}</span>
              <span class="font-normal color-grey">{{ $t('ARTICLE.MEDIA_FORMAT_DETAILS') }}</span>
              <span class="article-item-details-text-mark color-red">*</span>
            </div>
            <div
              v-if="isEdit"
              :class="{
                'simple-data-tooltip simple-data-tooltip-edge': isArticleIncludeInHero
              }"
              :data-tooltip-text="isArticleIncludeInHero && $t('ARTICLE_IN_HERO')"
            >
              <button
                type="button"
                class="btn-red-text"
                :disabled="isArticleIncludeInHero"
                @click="deleteArticles"
              >
                <img alt="delete" src="@/assets/images/delete-icon.png"/>
                <span>{{ $t('ARTICLE.DELETE_ARTICLE') }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="article-item-hr"></div>
      <div class="article-item-settings">
        <div class="article-item-settings-container">
          <div class="article-item-settings-name">
            <span class="font-size-20 font-bold color-black-mine-shaft">{{ $t('ARTICLE.CATEGORY') }} <span class="color-red">*</span></span>
          </div>
          <div class="article-item-settings-actions">
            <select-option
              class="article-item-settings-actions-select"
              v-model="category"
              :options="articlesCategories"
              placeholder="Select Category"
              @select-option:add-new="openAddNewCategoryModal"
            />
          </div>
        </div>
        <div class="article-item-settings-container">
          <div class="article-item-settings-name">
            <span class="font-size-base font-bold color-black-mine-shaft">{{ $t('ARTICLE.LABEL') }}:</span>
          </div>
          <div class="article-item-settings-actions">
            <label class="article-item-settings-actions-checkbox font-family-averta checkbox checkbox-silver">
              <img alt="new icon" src="@/assets/images/Newlabel.svg?skipsvgo=true" />
              <span class="font-size-18 color-black">{{ $t('ARTICLE.NEW') }}</span>
              <input type="checkbox" v-model="isNew">
              <span class="checkmark"></span>
            </label>

            <label class="article-item-settings-actions-checkbox font-family-averta checkbox checkbox-silver">
              <img alt="video icon" src="@/assets/images/Videolabel.svg?skipsvgo=true" />
              <span class="font-size-18 color-black">{{ $t('ARTICLE.VIDEO') }}</span>
              <input type="checkbox" v-model="hasVideo">
              <span class="checkmark"></span>
            </label>
          </div>
        </div>
      </div>
    </page-top-block>

    <simple-content-wrapper class="article-item-pages-wrapper">
      <article-item-pages />
    </simple-content-wrapper>
  </content-wrapper>
</template>

<script setup>
import { ref, onMounted } from "vue";
import SelectOption from "../../select-option.vue";
import ImageBox from "../../image-box.vue";
import PageTopBlock from "../../page-top-block.vue";
import ContentWrapper from "../../content-wrapper/content-wrapper.vue";
import NameField from "../../name-field.vue";
import ButtonWithSwitcher from "../../button-with-switcher.vue";
import SimpleContentWrapper from "../../simple-content-wrapper.vue";
import ArticleItemPages from "./article-item-pages.vue";
import { useArticleItemStore } from "../../../stores/article-item.js";
import { useI18n } from "vue-i18n";
import ConfirmModal from "../../modals/confirm-modal.vue";
import { CONFIRM_MODAL_TYPE } from "../../../models/confirm-modal.model.js";
import { useArticlesStore } from "../../../stores/articles.js";
import ArticleNewCategoryModal from "./article-new-category-modal.vue";
import AlertModal from "../../modals/alert-modal.vue";
import { useStore } from "vuex";
import ArticleItemPreviewModal from "./article-item-preview-modal.vue";
import ProcessModal from "../../modals/process-modal.vue";
import { PROCESS_MODAL_TYPE } from "../../../models/process-modal.model.js";
import { useImageUpload } from '../../../composables/useImageUpload';

const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const { $keys } = useNuxtApp();
const store = useStore();
const route = useRoute();
const { readyProject } = useProjectLang();
const { triggerLoading } = useCommonUtils();
const {
  articlesCategories,
  getArticlesCategoriesAsync,
} = useArticlesStore();
const {
  uuid,
  title,
  state,
  image,
  category,
  pages,
  isNew,
  hasVideo,
  hasChanges,
  postArticleAsync,
  patchArticleAsync,
  deleteArticleAsync,
  resetStore,
  setInitialPayload,
} = useArticleItemStore();
const { t } = useI18n();
const { openModal, closeModal } = useBaseModal({
  "ArticleNewCategoryModal": {
    component: ArticleNewCategoryModal,
    hideCloseBtn: false,
  },
  "ArticleItemPreviewBeforeSaveModal": {
    component: ArticleItemPreviewModal,
    hideCloseBtn: false,
  },
  "ArticleItemAlertModal": AlertModal,
  "ArticleItemProcessModal": {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
  },
  "ArticleItemConfirmModal": ConfirmModal,
});

const isReady = ref(false);
const lang = ref("");
const isArticleIncludeInHero = ref(false);
const stateKeys = reactive({
  PUBLISHED: t('ARTICLE.PUBLISHED'),
  UNPUBLISHED: t('ARTICLE.UNPUBLISHED'),
});

const isPublish = computed(() => state.value === stateKeys.PUBLISHED);
const hintID = computed(() => props.isEdit ? `ID: ${uuid.value}` : "");
const isArticleValid = computed(() => !(!image.value || !title.value || !category.value || !pages.value.length));

const openAddNewCategoryModal = () => openModal({ name: "ArticleNewCategoryModal" });

const { 
  uploadFile, 
  uploadImagePercentage: imageUploadPercentage, 
  loadedImageSize: imageLoadedSize, 
  imageResponseUrl: imageResponseUrl 
} = useImageUpload({
  entity: 'article',
  onImageUploaded: (result) => {
    image.value = result;
    if (props.isEdit && !isInitializing.value) {
      hasChanges.value = true;
    }
  },
  getPreSignedUrl: (params) => store.dispatch("preSignedUrl/getPreSignedImageUrlAsync", { 
    isin: uuid.value, 
    params: {
      ...params,
      lang: lang.value
    }
  })
});

const handleFileUpload = (file) => {
  const result = uploadFile(file);
  
  if (result.shouldShowWarning) {
    openModal({
      name: "confirmModal",
      props: {
        modalType: CONFIRM_MODAL_TYPE.CONTINUE_LOAD,
        title: $t('DESCRIPTION_POPUP.LARGER_FILE'),
        descriptionRed: $t('DESCRIPTION_POPUP.OPTIMAL_IMAGE'),
      },
      onClose: (response) => response && result.upload(),
    });
  } else if (result.shouldShowError) {
    openModal({
      name: "confirmModal",
      props: {
        modalType: CONFIRM_MODAL_TYPE.UNABLE,
        title: $t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE'),
        description: $t('DESCRIPTION_POPUP.MAX_IMAGE'),
        hideCancelBtn: true
      },
    });
  } else if (result.shouldUpload) {
    result.upload();
  }
};

const publishArticleAction = () => {
  if(!isArticleValid.value) {
    openModal({
      name: "ArticleItemConfirmModal",
      props: {
        modalType: CONFIRM_MODAL_TYPE.UNABLE,
        title: "Unable to Publish.",
        description: "Please fill in all Required data.",
        hideCancelBtn: true,
      }
    });
  } else if (state.value === stateKeys.PUBLISHED) {
    openModal({
      name: "ArticleItemConfirmModal",
      props: {
        modalType: CONFIRM_MODAL_TYPE.UNPUBLISH,
        title: "Do you want to unpublish this article?",
        confirmBtnLabel: "UNPUBLISH"
      },
      onClose: (response) => response && (state.value = stateKeys.UNPUBLISHED),
    });
  } else if (state.value === stateKeys.UNPUBLISHED) {
    openModal({
      name: "ArticleItemConfirmModal",
      props: {
        modalType: CONFIRM_MODAL_TYPE.SAVE,
        title: t('DESCRIPTION_POPUP.PUBLISH_POPUP'),
        confirmBtnLabel: t('BUTTONS.CONFIRM_BUTTON'),
      },
      onClose: (response) => response && (state.value = stateKeys.PUBLISHED),
    });
  }
};

const openPreviewSaveModal = () => {
  openModal({
    name: "ArticleItemPreviewBeforeSaveModal",
    props: {
      isActionsEnabled: true,
      confirmBtnLabel: state.value === stateKeys.PUBLISHED ? t('BUTTONS.PUBLISH_BUTTON') : t('BUTTONS.SAVE_BUTTON'),
    },
    onClose: (response) => response && saveArticlesAsync(),
  });
};

const saveArticlesAsync = async () => {
  try {
    openModal({
      name: "ArticleItemProcessModal",
      props: {
        modalType: state.value === stateKeys.PUBLISHED ? PROCESS_MODAL_TYPE.PUBLISHING : PROCESS_MODAL_TYPE.SAVING,
      },
    });
    const response = props.isEdit ? await patchArticleAsync({ lang: lang.value }) : await postArticleAsync({ lang: lang.value });
    if (response) {
      navigateTo("/articles");

      if (state.value === stateKeys.UNPUBLISHED) {
        triggerLoading($keys.KEY_NAMES.ARTICLE_SUCCESS);
      } else if (state.value === stateKeys.PUBLISHED) {
        triggerLoading($keys.KEY_NAMES.ARTICLE_PUBLISHED_SUCCESS);
      }
    }
  } catch {
    triggerLoading($keys.KEY_NAMES.ARTICLE_WRONG);
  } finally {
    closeModal("ArticleItemProcessModal");
  }
};

const deleteArticles = () => {
  openModal({
    name: "ArticleItemConfirmModal",
    props: {
      modalType: CONFIRM_MODAL_TYPE.DELETE,
      title: "Delete the article?",
      description: `${t('DESCRIPTION_POPUP.DELETE_POPUP')} article?`,
    },
    onClose: (response) => response && deleteArticleMasterDataAsync(),
  });
};

const deleteArticleMasterDataAsync = async () => {
  try {
    openModal({
      name: "ArticleItemProcessModal",
      props: {
        modalType: PROCESS_MODAL_TYPE.DELETING,
      },
    });
    await deleteArticleAsync({ lang: lang.value });
    navigateTo("/articles");
    triggerLoading($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
  } catch {
    navigateTo("/articles");
    triggerLoading($keys.KEY_NAMES.ARTICLE_WRONG);
  } finally {
    closeModal("ArticleItemProcessModal");
  }
};

watch(isNew, (val) => {
  if (val && isReady.value) {
    triggerLoading($keys.KEY_NAMES.NEW_SUCCESS);
  } else {
    isReady.value = true;
  }
});

onBeforeRouteLeave((to, from, next) => {
  if (hasChanges.value) {
    openModal({
      name: "ArticleItemConfirmModal",
      props: {
        modalType: CONFIRM_MODAL_TYPE.EXIT,
        title: t('DESCRIPTION_POPUP.EXIT_PAGE_POPUP'),
      },
      onClose: (response) => next(response),
    });
  } else {
    next();
  }
});

onMounted(() => {
  readyProject( async ({ isProjectReady }) => {
    if (isProjectReady) {
      await getArticlesCategoriesAsync();
      lang.value =  store.getters["userData/getDefaultLang"];
    }
  });

  isArticleIncludeInHero.value = route.query?.isArticleIncludedInHero === "true";

  if (!props.isEdit) {
    state.value = stateKeys.UNPUBLISHED;
    setInitialPayload();
  }
});

onBeforeUnmount(() => {
  resetStore();
});
</script>
